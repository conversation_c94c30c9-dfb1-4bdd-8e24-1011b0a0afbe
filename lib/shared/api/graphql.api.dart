import 'dart:async';
import 'dart:convert';

import 'package:ambulancia_app/models/config_app_ambulancia_constants_model.dart';
import 'package:ambulancia_app/models/destiny_model.dart';
import 'package:ambulancia_app/models/movements_model.dart';
import 'package:ambulancia_app/models/pre_hospital_discharge_clinical_picture_model.dart';
import 'package:ambulancia_app/models/reason_pre_hospital_discharge_model.dart';
import 'package:ambulancia_app/models/user_model.dart';
import 'package:ambulancia_app/shared/api/auth.api.dart';
import 'package:ambulancia_app/shared/exceptions.dart';
import 'package:ambulancia_app/shared/http-client.dart';
import 'package:ambulancia_app/shared/locator.dart';
import 'package:ambulancia_app/shared/messages.exceptions.dart';
import 'package:ambulancia_app/shared/utils/datetime_utils.dart';
import 'package:ambulancia_app/shared/utils/logger_print.dart';
import 'package:ambulancia_app/shared/utils/offline-first/offline_first.dart';
import 'package:ambulancia_app/shared/utils/offline-first/sqlite/tables/response.table.dart';
import 'package:ambulancia_app/shared/utils/sync.utils.dart';
import 'package:graphql/client.dart';

class GraphQlApi {
  final UnimedHttpClient httpClient;

  GraphQlApi(this.httpClient);

  final logger = UnimedLogger(className: 'GraphQlApi');

  Future<GraphQLClient> getGithubGraphQLClient() async {
    final tokenPerfilApps =
        await Locator.instance.get<AuthApi>().tokenPerfilAppsNew();

    String url = const String.fromEnvironment("graphql");

    final Link link = HttpLink(
      url,
      defaultHeaders: {
        'Authorization': 'Bearer $tokenPerfilApps',
      },
    );

    return GraphQLClient(
      cache: GraphQLCache(),
      link: link,
      queryRequestTimeout: const Duration(seconds: 120),
    );
  }

  Future<ConfigAppAmbulanciaConstants> getConfigAppAmbulanciaConstants(
      {required String userId}) async {
    try {
      final GraphQLClient client = await getGithubGraphQLClient();

      String query = '''
        query ConfigAppAmbulanciaConstants {
          configAppAmbulanciaConstants(userId: "$userId") {
              nervous {
                  glasgowComaScaleMin
                  glasgowComaScaleMax
              }
              cardiovascular {
                  heartRateMin
                  heartRateMax
                  respiratoryRateMin
                  respiratoryRateMax
                  bloodPressureAMin
                  bloodPressureAMax
                  bloodPressureBMin
                  bloodPressureBMax
                  oxygenSaturationMin
                  oxygenSaturationMax
                  bloodGlucoseMin
                  bloodGlucoseMax
                  temperatureMin
                  temperatureMax
              }
              imageQuality {
                  maxHeightDefault
                  maxQualityDefault
              }
          }
      }
      ''';

      logger.d('getConfigAppAmbulanciaConstants query : $query');

      final QueryOptions options = QueryOptions(
        document: gql(
          query,
        ),
      );

      final QueryResult result = await client.query(options);

      if (result.hasException) {
        logger.e(
            'getConfigAppAmbulanciaConstants exception : ${result.exception}');
        throw UnimedException(MessageException.general);
      } else {
        final Map<String, dynamic> data = result
            .data!["configAppAmbulanciaConstants"] as Map<String, dynamic>;
        logger.d('getConfigAppAmbulanciaConstants success : $data');

        return ConfigAppAmbulanciaConstants.fromJson(data);
      }
    } on UnimedException catch (ex) {
      logger.e('getConfigAppAmbulanciaConstants ${ex.runtimeType} : $ex');
      throw UnimedException(ex.message);
    } catch (ex) {
      logger.e('getConfigAppAmbulanciaConstants exception : $ex');
      throw UnimedException(MessageException.general);
    }
  }

  Future<void> removeSupply({
    required int? attendanceNumber,
    required int? supplyCode,
  }) async {
    try {
      final GraphQLClient client = await getGithubGraphQLClient();

      String mutation = '''
        mutation AmbulanceRemoveSupply {
          ambulanceRemoveSupply(attendance: "$attendanceNumber", supplyCode: $supplyCode) {
            message
          }
        }
      ''';

      logger.d('removeSupply mutation: $mutation');

      final QueryOptions options = QueryOptions(
        document: gql(
          mutation,
        ),
      );

      final QueryResult result =
          await client.query(options).timeout(const Duration(seconds: 60));

      if (result.hasException) {
        logger.e('removeSupply exception: ${result.exception}');
        // Verifica se é erro de conexão (NetworkException)
        final isNetworkError = result.exception?.linkException != null;
        if (isNetworkError) {
          throw NoInternetException();
        }
        final message = (result.exception?.graphqlErrors.isNotEmpty == true)
            ? result.exception!.graphqlErrors.first.message
            : MessageException.general;
        throw UnimedException(message + "  (-1000)");
      } else {
        logger.d('removeSupply success: ${result.data}');
        final data = result.data?['ambulanceRemoveSupply'];
        logger.d('removeSupply success');
        if (data == null) {
          throw UnimedException(MessageException.general);
        }
      }
    } on TimeoutException catch (e) {
      logger.e('removeSupply error timeout $e');
      throw UnimedException(MessageException.genericTimeout);
    } on UnimedException catch (ex) {
      logger.e('removeSupply UnimedException: ${ex.runtimeType} : $ex');
      throw UnimedException(ex.message);
    } catch (ex) {
      logger.e('removeSupply exception: $ex');
      throw UnimedException(MessageException.general);
    }
  }

  Future<MovementsModel?> getAttendanceMovements(dynamic attendance) async {
    try {
      final GraphQLClient client = await getGithubGraphQLClient();

      String query = '''
      query AmbulanceGetMovimentsByAttendance {
        ambulanceGetMovimentsByAttendance(attendance: "$attendance") {
          attendance
          moviments {
            movimentDate
            statusCode
            statusName
          }
          requiresReasonForDelayLeavingBase
          requestsAttendanceTypeAttendance
          isReclassified
        }
      }
    ''';

      logger.d('getAttendanceMovements GraphQL query : $query');

      final QueryOptions options = QueryOptions(
        document: gql(query),
      );

      final QueryResult result = await client.query(options);

      if (result.hasException) {
        logger.e('GraphQL exception: ${result.exception}');
        return _getAttendaceMovementsOfflineMerged(
          attendance: attendance,
          exception: NoInternetException(),
        );
      } else {
        final data = result.data?['ambulanceGetMovimentsByAttendance'];
        if (data == null) {
          return _getAttendaceMovementsOfflineMerged(
            attendance: attendance,
            exception: NoInternetException(),
          );
        }

        final jsonString = jsonEncode(data);
        await _persistGetAttendaceMovements(
          numAtendimento: attendance,
          value: jsonString,
        );

        logger.d('GraphQL success response: $data');

        final resultMerged = await _getAttendaceMovementsOfflineMerged(
          attendance: attendance,
        );
        if (resultMerged != null) {
          return resultMerged;
        } else {
          return MovementsModel.fromJson(data);
        }
      }
    } on NoInternetException catch (ex) {
      logger.i('NoInternetException: ${ex.message}');
      return _getAttendaceMovementsOfflineMerged(
        attendance: attendance,
        exception: ex,
      );
    } on ServiceTimeoutException catch (ex) {
      logger.e('ServiceTimeoutException: $ex');
      return _getAttendaceMovementsOfflineMerged(
        attendance: attendance,
        exception: ex,
      );
    } on UnimedException catch (ex) {
      logger.e('UnimedException: $ex');
      return _getAttendaceMovementsOfflineMerged(
        attendance: attendance,
        exception: ex,
      );
    } catch (ex) {
      logger.e('Unexpected Exception: $ex');
      return _getAttendaceMovementsOfflineMerged(
        attendance: attendance,
        exception: ServiceTimeoutException(),
      );
    }
  }

//PARTE OFFLINE - PARA QUANDO NÃO HÁ INTERNET
  Future<MovementsModel?> _getAttendaceMovementsOfflineMerged(
      {required attendance, UnimedException? exception}) async {
    try {
      MovementsModel? valuesCurrent =
          await _getResponseOfflineToIdMovements(attendance);
      final listRequest = await OfflineFirst.getCacheRequest(
          customID:
              '${SYNC_CATEGORY_API.AttendanceApi.name}${SYNC_ATTENDANCEAPI_REQUEST.updateStatusMovements.name}$attendance');

      if (listRequest != null) {
        listRequest.forEach(
          (element) {
            final value = jsonDecode(element.jsonRequest!);
            if (valuesCurrent != null) {
              valuesCurrent!.moviments.add(MovementObject(
                  statusCode: int.parse(value['codStatus']),
                  movimentDate: DateTimeUtils.getTimeConvertMovementsModel(
                      date: value['dataHora'])));
            } else {
              valuesCurrent = MovementsModel(
                attendance: int.parse(attendance),
                moviments: [
                  MovementObject(
                      statusCode: int.parse(value['codStatus']),
                      movimentDate: DateTimeUtils.getTimeConvertMovementsModel(
                          date: value['dataHora']))
                ],
              );
            }
          },
        );
      }

      if (valuesCurrent != null) {
        return valuesCurrent;
      } else {
        if (exception != null) throw exception;
      }
    } catch (e) {
      logger.e('_getAttendaceMovementsOffline error $e');
      if (exception != null) throw exception;
    }
    return null;
  }

  _persistGetAttendaceMovements(
      {required value, required numAtendimento}) async {
    try {
      final sqliteTableResponse = ResponseTableSQLite();
      final LoginDataModel? loginDataModel =
          await Locator.instance.get<AuthApi>().getCredentials();

      ResponseRecordSQLite response = ResponseRecordSQLite(
          customId:
              '${SYNC_CATEGORY_API.AttendanceApi.name}${SYNC_ATTENDANCEAPI_REQUEST.getAttendanceMovements.name}${numAtendimento}_${loginDataModel?.userCredentials!.userClean}_${loginDataModel?.vehicleModel?.nomeVeiculo.toString().replaceAll(' ', '_')}',
          value: value,
          category: '$numAtendimento');
      await sqliteTableResponse.addOrUpdate(response);
    } catch (e) {
      logger.e('_persistGetAttendaceMovements catch Exception $e');
    }
  }

  Future<MovementsModel?> _getResponseOfflineToIdMovements(
      numAtendimento) async {
    final valuesCurrentJson = await OfflineFirst.getCacheResponse(
        customID:
            '${SYNC_CATEGORY_API.AttendanceApi.name}${SYNC_ATTENDANCEAPI_REQUEST.getAttendanceMovements.name}$numAtendimento');
    MovementsModel? valuesCurrent;
    if (valuesCurrentJson != null) {
      final decoded = jsonDecode(valuesCurrentJson);
      // Garante que a lista nunca seja null
      if (decoded['movimentacoes'] == null && decoded['moviments'] == null) {
        decoded['movimentacoes'] = [];
      }
      valuesCurrent = MovementsModel.fromJson(decoded);
    }
    return valuesCurrent;
  }

  Future<List<ReasonPreHospitalDischargeModel>> getReasonPreHospitalDischarge(
      {required int codUnimed}) async {
    try {
      final GraphQLClient client = await getGithubGraphQLClient();

      String query = '''
       query AmbulanceReasonPreHospitalDischarge {
            ambulanceReasonPreHospitalDischarge(codUnimed: $codUnimed) {
                codeReason
                nameReason
            }
        }
      ''';

      logger.d('getReasonPreHospitalDischarge GraphQL query : $query');

      final QueryOptions options = QueryOptions(
        document: gql(query),
      );

      final QueryResult result = await client.query(options);

      if (result.hasException) {
        logger.e(
            'AmbulanceReasonPreHospitalDischarge GraphQL exception : ${result.exception}');
        throw UnimedException(MessageException.general);
      } else {
        final List<dynamic>? data =
            result.data?['ambulanceReasonPreHospitalDischarge'];
        if (data == null) {
          throw UnimedException(MessageException.general);
        }

        logger.d(
            'AmbulanceReasonPreHospitalDischarge GraphQL success response: $data');

        return data
            .map((item) => ReasonPreHospitalDischargeModel.fromJson(item))
            .toList();
      }
    } catch (ex) {
      logger.e('getReasonPreHospitalDischarge exception: $ex');
      throw UnimedException(MessageException.general);
    }
  }

  Future<List<PreHospitalDischargeClinicalPictureModel>>
      getPreHospitalDischargeClinicalPicture({required int codUnimed}) async {
    try {
      final GraphQLClient client = await getGithubGraphQLClient();

      String query = '''
       query AmbulancePreHospitalDischargeClinicalPicture {
          ambulancePreHospitalDischargeClinicalPicture(codUnimed: $codUnimed) {
              code
              nameClinicalPicture
          }
      }
      ''';

      logger.d('getPreHospitalDischargeClinicalPicture GraphQL query : $query');

      final QueryOptions options = QueryOptions(
        document: gql(query),
      );

      final QueryResult result = await client.query(options);

      if (result.hasException) {
        logger.e(
            'AmbulancePreHospitalDischargeClinicalPicture GraphQL exception : ${result.exception}');
        throw UnimedException(MessageException.general);
      } else {
        final List<dynamic>? data =
            result.data?['ambulancePreHospitalDischargeClinicalPicture'];
        if (data == null) {
          throw UnimedException(MessageException.general);
        }

        logger.d(
            'AmbulancePreHospitalDischargeClinicalPicture GraphQL success response: $data');

        return data
            .map((item) =>
                PreHospitalDischargeClinicalPictureModel.fromJson(item))
            .toList();
      }
    } catch (ex) {
      logger.e('getPreHospitalDischargeClinicalPicture exception: $ex');
      throw UnimedException(MessageException.general);
    }
  }


  Future<List<DestinyModel> listDestinies({required int codUnimed}) async {
    try{
       final GraphQLClient client = await getGithubGraphQLClient();
     
      String query = '''
      query AmbulanceAvailableDestinations {
      ambulanceAvailableDestinations(codUnimed: $codUnimed) {
        codUnimed
        patientDestinationCode
        patientDestinationName
      }
    }
        }
      }
    ''';

      logger.d('listDestinies GraphQL query : $query');

      final QueryOptions options = QueryOptions(
        document: gql(query),
      );

      final QueryResult result = await client.query(options);
      if (result.hasException) {
        logger.e('listDestinies exception: ${result.exception}');
        throw UnimedException(MessageException.general + " (-1000)");
      } else {
        final List<dynamic>? data =
            result.data?['ambulanceAvailableDestinations'];
        if (data == null) {
          throw UnimedException(MessageException.general) ;
        }
        logger.d('listDestinies success response: $data');
    }

    } on NoInternetException catch(ex){
      logger.i('listDestinies NoInternetException ${ex.message}');
      throw NoInternetException();
    } on ServerTimeoutException catch (ex){

    }
     
}
}

